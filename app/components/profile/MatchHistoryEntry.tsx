import React from "react";
import { View, type ViewStyle } from "react-native";
import { HeroName } from "@/components/heroes/HeroName";
import { Card } from "@/components/ui/Card";
import { Text } from "@/components/ui/Text";
import type { MatchHistory } from "@/services/api/types/match_history";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";
import { formatMatchDuration, formatRelativeTime, isMatchWon } from "@/utils/matchHistoryStats";

export interface MatchHistoryEntryProps {
  /**
   * The match history data to display
   */
  match: MatchHistory;
}

/**
 * Individual match history entry component that displays match details
 */
export const MatchHistoryEntry = React.memo<MatchHistoryEntryProps>((props) => {
  const { match } = props;
  const { themed, theme } = useAppTheme();

  const won = isMatchWon(match);
  const duration = formatMatchDuration(match.match_duration_s);
  const timeAgo = formatRelativeTime(match.start_time);

  return (
    <Card
      style={themed($cardContainer)}
      contentStyle={[
        themed($cardContent),
        won ? { backgroundColor: theme.colors.palette.primary100 } : { backgroundColor: theme.colors.palette.angry100 },
      ]}
    >
      {/* Match Result Indicator */}
      <View
        style={[
          themed($resultIndicator),
          { backgroundColor: won ? theme.colors.palette.primary500 : theme.colors.error },
        ]}
      />

      {/* Main Content */}
      <View style={themed($contentContainer)}>
        {/* Hero and Result Row */}
        <View style={themed($topRow)}>
          <View style={themed($heroContainer)}>
            <HeroName hero_id={match.hero_id} />
          </View>

          <View style={themed($resultContainer)}>
            <Text
              text={won ? "WIN" : "LOSS"}
              style={[themed($resultText), { color: won ? theme.colors.palette.primary600 : theme.colors.error }]}
            />
          </View>
        </View>

        {/* Stats Row */}
        <View style={themed($statsRow)}>
          <View style={themed($statItem)}>
            <Text text="KDA" preset="formLabel" style={themed($statLabel)} />
            <Text
              text={`${match.player_kills}/${match.player_deaths}/${match.player_assists}`}
              style={themed($statValue)}
            />
          </View>

          <View style={themed($statItem)}>
            <Text text="Duration" preset="formLabel" style={themed($statLabel)} />
            <Text text={duration} style={themed($statValue)} />
          </View>

          <View style={themed($statItem)}>
            <Text text="Net Worth" preset="formLabel" style={themed($statLabel)} />
            <Text text={match.net_worth.toLocaleString()} style={themed($statValue)} />
          </View>
        </View>

        {/* Time Row */}
        <View style={themed($timeRow)}>
          <Text text={timeAgo} preset="formHelper" style={themed($timeText)} />
        </View>
      </View>
    </Card>
  );
});

MatchHistoryEntry.displayName = "MatchHistoryEntry";

const $cardContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginHorizontal: spacing.md,
  marginVertical: spacing.xs,
});

const $cardContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.md,
  flexDirection: "row",
  alignItems: "stretch",
});

const $resultIndicator: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 4,
  borderRadius: 2,
  marginRight: spacing.md,
});

const $contentContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
});

const $topRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.sm,
});

const $heroContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
});

const $resultContainer: ThemedStyle<ViewStyle> = () => ({
  alignItems: "flex-end",
});

const $resultText: ThemedStyle<ViewStyle> = () => ({
  fontSize: 14,
  fontWeight: "bold",
});

const $statsRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  marginBottom: spacing.sm,
});

const $statItem: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  alignItems: "center",
});

const $statLabel: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.textDim,
  fontSize: 10,
  marginBottom: 2,
});

const $statValue: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.text,
  fontSize: 12,
  fontWeight: "600",
});

const $timeRow: ThemedStyle<ViewStyle> = () => ({
  alignItems: "flex-end",
});

const $timeText: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.textDim,
  fontSize: 10,
});
