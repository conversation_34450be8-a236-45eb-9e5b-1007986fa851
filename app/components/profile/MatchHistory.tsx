import React, { useMemo } from "react";
import { ActivityIndicator, FlatList, type ListRenderItem, View, type ViewStyle } from "react-native";
import { Text } from "@/components/ui/Text";
import { useMatchHistory } from "@/hooks/useMatchHistory";
import type { MatchHistory as MatchHistoryType } from "@/services/api/types/match_history";
import { useAppTheme } from "@/theme/context";
import type { ThemedStyle } from "@/theme/types";
import { filterLast30Days } from "@/utils/matchHistoryStats";
import { MatchHistoryEntry } from "./MatchHistoryEntry";

export interface MatchHistoryProps {
  /**
   * Steam ID of the player
   */
  steamId: number | null;
}

/**
 * Component that displays a scrollable list of recent match entries
 */
export function MatchHistory(props: MatchHistoryProps) {
  const { steamId } = props;
  const { themed, theme } = useAppTheme();

  const { data: matchHistoryData, isLoading, error } = useMatchHistory(steamId);

  // Filter matches to last 30 days and sort by start_time (most recent first)
  const recentMatches = useMemo(() => {
    if (!matchHistoryData) return [];
    const matches = Array.isArray(matchHistoryData) ? matchHistoryData : [matchHistoryData];
    const filtered = filterLast30Days(matches);
    return filtered.sort((a, b) => b.start_time - a.start_time);
  }, [matchHistoryData]);

  const renderMatchEntry: ListRenderItem<MatchHistoryType> = ({ item }) => <MatchHistoryEntry match={item} />;

  const keyExtractor = (item: MatchHistoryType) => item.match_id.toString();

  const renderEmptyComponent = () => (
    <View style={themed($emptyContainer)}>
      <Text text="No matches found in the last 30 days" preset="formHelper" style={themed($emptyText)} />
    </View>
  );

  const renderLoadingComponent = () => (
    <View style={themed($loadingContainer)}>
      <ActivityIndicator size="large" color={theme.colors.tint} />
      <Text text="Loading match history..." style={themed($loadingText)} />
    </View>
  );

  const renderErrorComponent = () => (
    <View style={themed($errorContainer)}>
      <Text text="Failed to load match history" preset="formHelper" style={themed($errorText)} />
    </View>
  );

  if (isLoading) {
    return renderLoadingComponent();
  }

  if (error) {
    return renderErrorComponent();
  }

  return (
    <View style={themed($container)}>
      {/* Section Title */}
      <Text text="Recent Matches (Last 30 Days)" preset="subheading" style={themed($sectionTitle)} />

      {/* Match List */}
      <FlatList
        data={recentMatches}
        renderItem={renderMatchEntry}
        keyExtractor={keyExtractor}
        ListEmptyComponent={renderEmptyComponent}
        showsVerticalScrollIndicator={false}
        style={themed($flatList)}
        contentContainerStyle={themed($flatListContent)}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={5}
        getItemLayout={(data, index) => ({
          length: 120, // Approximate height of each item
          offset: 120 * index,
          index,
        })}
      />
    </View>
  );
}

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingVertical: spacing.md,
});

const $sectionTitle: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  color: colors.text,
  marginBottom: spacing.md,
  marginHorizontal: spacing.md,
  textAlign: "center",
});

const $flatList: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
});

const $flatListContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingBottom: spacing.lg,
});

const $loadingContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingVertical: spacing.xl,
});

const $loadingText: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  marginTop: spacing.md,
  color: colors.textDim,
});

const $errorContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingVertical: spacing.xl,
});

const $errorText: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.error,
  textAlign: "center",
});

const $emptyContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingVertical: spacing.xl,
});

const $emptyText: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.textDim,
  textAlign: "center",
});
